#!/bin/bash
set -euo pipefail

echo "Installing Syncthing..."

# Add Syncthing repository and install
curl -s https://syncthing.net/release-key.txt | sudo gpg --dearmor -o /usr/share/keyrings/syncthing-archive-keyring.gpg
echo "deb [signed-by=/usr/share/keyrings/syncthing-archive-keyring.gpg] https://apt.syncthing.net/ syncthing stable" | sudo tee /etc/apt/sources.list.d/syncthing.list > /dev/null

sudo apt update
sudo apt install -y syncthing

# Enable user service to persist across reboots
sudo loginctl enable-linger "$USER"
systemctl --user enable syncthing
systemctl --user start syncthing

echo "Syncthing installed and started as user service"
echo "Web UI available at: http://localhost:8384"