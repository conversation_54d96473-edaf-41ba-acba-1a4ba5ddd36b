#!/usr/bin/env bash
set -euo pipefail

CONFIG_FILE="${1:-}"
[[ -z "$CONFIG_FILE" || ! -f "$CONFIG_FILE" ]] && {
  echo "Usage: $0 <device_config.yaml>"
  exit 1
}

API_URL="http://localhost:8384/rest"
API_KEY="${SYNCTHING_API_KEY:-}"
HEADERS=()
[[ -n "$API_KEY" ]] && HEADERS+=(-H "X-API-Key: $API_KEY")

for cmd in jq yq curl; do
  command -v "$cmd" >/dev/null || { echo >&2 "$cmd is required"; exit 1; }
done

# Ensure Syncthing is up
curl -fs "${HEADERS[@]}" "$API_URL/system/ping" | grep -q pong || {
  echo "Syncthing not running or API key invalid"
  exit 1
}

DEVICE_ID=$(yq eval '.device_id' "$CONFIG_FILE")
[[ "$DEVICE_ID" == "null" ]] && {
  echo "device_id missing from $CONFIG_FILE"
  exit 1
}

# Clear existing folders
config_json=$(curl -s "${HEADERS[@]}" "$API_URL/system/config")
config_json=$(echo "$config_json" | jq '.folders = []')

# Inject folders
while IFS= read -r folder; do
  id=$(echo "$folder" | jq -r '.id')
  path=$(echo "$folder" | jq -r '.path')

  folder_json=$(jq -n \
    --arg id "$id" \
    --arg path "$path" \
    --arg device "$DEVICE_ID" \
    '{
      id: $id,
      label: $id,
      path: $path,
      type: "sendreceive",
      devices: [ { deviceID: $device } ],
      rescanIntervalS: 3600,
      fsWatcherEnabled: true,
      fsWatcherDelayS: 10
    }'
  )

  config_json=$(echo "$config_json" | jq --argjson folder "$folder_json" '.folders += [$folder]')
done < <(
  yq eval '.folders | to_entries | .[] | {id: .key, path: .value.path}' "$CONFIG_FILE" | yq -o=json
)

# Apply config
curl -s -X POST "${HEADERS[@]}" \
  -H "Content-Type: application/json" \
  -d "$config_json" \
  "$API_URL/system/config"

# Reload
curl -s -X POST "${HEADERS[@]}" "$API_URL/system/restart"

echo "Syncthing config applied from $CONFIG_FILE"
